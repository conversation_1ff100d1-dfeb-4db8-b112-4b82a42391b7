# Ecommerce Golang Clean Architecture - Postman Collection

## 📋 Overview

This comprehensive Postman collection provides complete API testing coverage for the Ecommerce Golang Clean Architecture project. It includes all endpoints with proper authentication, environment variables, and automated test scripts.

## 🚀 Quick Start

### 1. Import Collection & Environment

1. **Import Collection**: Import `Ecommerce-API-Collection.postman_collection.json`
2. **Import Environment**: Import `Ecommerce-API-Environment.postman_environment.json`
3. **Select Environment**: Choose "Ecommerce Golang Clean Architecture Environment" in Postman

### 2. Configure Base URL

Update the `base_url` environment variable:
- **Development**: `http://localhost:8080`
- **Production**: `https://your-domain.com`

### 3. Authentication Setup

The collection includes automated token management:

1. **Register/Login**: Use Authentication endpoints to get JWT tokens
2. **Auto Token Storage**: Tokens are automatically stored in environment variables
3. **Auto Refresh**: Pre-request scripts handle token refresh when needed

## 📁 Collection Structure

### 🔐 Authentication (9 endpoints)
- User registration, login, logout
- Password reset and email verification
- Token refresh and session management

### 👤 User Management (6 endpoints)
- Profile management and preferences
- Password changes and session handling

### 🛍️ Product Management (8 endpoints)
- **Public Operations**: Browse, search, and view products
- **Admin Operations**: Create, update, delete products

### 📂 Category Management (5 endpoints)
- **Public Operations**: Browse category tree
- **Admin Operations**: Manage category hierarchy

### 📦 Order Management (5 endpoints)
- Order creation, tracking, and management
- Order history and status updates

### 🛒 Cart Management (6 endpoints)
- Cart operations for authenticated and guest users
- Cart merging and session management

### 💰 Payment Management (4 endpoints)
- Payment processing with Stripe integration
- Refund handling and payment tracking

### 📁 File Management (7 endpoints)
- **Multi-level Upload**: Public, User, Admin uploads
- File management and deletion

### 🔧 Admin Panel (7 endpoints)
- Dashboard analytics and system stats
- User management and system operations

## 🔧 Environment Variables

### Core Variables
- `base_url`: API base URL
- `jwt_token`: Authentication token (auto-managed)
- `refresh_token`: Refresh token (auto-managed)
- `user_id`: Current user ID (auto-set)

### Test Data Variables
- `test_email`: Default test email
- `test_password`: Default test password
- `admin_email`: Admin test email
- `admin_password`: Admin test password

### Dynamic Variables (Auto-managed)
- `created_product_id`: ID of newly created products
- `order_id`: Order IDs for testing
- `payment_id`: Payment transaction IDs
- `uploaded_file_id`: File upload IDs
- And many more...

## 🧪 Testing Features

### Automated Test Scripts
Each endpoint includes comprehensive test scripts:
- **Status Code Validation**: Ensures proper HTTP responses
- **Response Structure Validation**: Verifies JSON structure
- **Data Integrity Checks**: Validates response data
- **Environment Variable Management**: Auto-stores IDs for chaining

### Pre-request Scripts
Global pre-request scripts handle:
- **Token Refresh**: Automatic JWT token renewal
- **Session Management**: Guest session handling
- **Request Preparation**: Dynamic data generation

### Test Scenarios
- **Happy Path Testing**: All endpoints with valid data
- **Error Handling**: Invalid requests and edge cases
- **Authentication Flow**: Complete auth lifecycle
- **Data Relationships**: Cross-endpoint data consistency

## 📊 Usage Patterns

### 1. Complete E-commerce Flow
```
1. Register/Login → Get JWT token
2. Browse Products → Get product IDs
3. Add to Cart → Create cart session
4. Create Order → Generate order
5. Process Payment → Complete transaction
```

### 2. Admin Operations
```
1. Admin Login → Get admin JWT
2. Create Category → Get category ID
3. Create Product → Link to category
4. Manage Orders → Update statuses
5. View Analytics → Monitor performance
```

### 3. File Upload Workflow
```
1. Upload Image (User/Admin/Public)
2. Get File Info → Verify upload
3. Use File URL → In products/categories
4. Delete File → Cleanup
```

## 🔍 Advanced Features

### Bulk Operations
- Bulk user management
- Batch product updates
- Mass email notifications

### Analytics Integration
- Sales metrics tracking
- User behavior analytics
- Product performance data

### Multi-level Authentication
- **Public**: No authentication required
- **User**: JWT token required
- **Admin**: Admin role required
- **Moderator**: Moderator role required

## 🛠️ Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check if JWT token is valid
   - Try refreshing token or re-login

2. **Environment Variables Not Set**
   - Ensure environment is selected
   - Check if test scripts are running

3. **Base URL Issues**
   - Verify server is running
   - Check base_url environment variable

### Debug Tips
- Enable Postman Console for detailed logs
- Check test results tab for script outputs
- Verify environment variables are populated

## 📈 Performance Testing

The collection supports performance testing:
- Use Postman Runner for load testing
- Configure iterations and delays
- Monitor response times and success rates

## 🔄 Continuous Integration

Collection can be integrated with CI/CD:
```bash
# Run collection with Newman
newman run Ecommerce-API-Collection.postman_collection.json \
  -e Ecommerce-API-Environment.postman_environment.json \
  --reporters cli,json
```

## 📝 Contributing

When adding new endpoints:
1. Follow existing naming conventions
2. Add comprehensive test scripts
3. Update environment variables as needed
4. Document new features in this README

## 🆘 Support

For issues or questions:
1. Check existing test scripts for examples
2. Verify environment variable setup
3. Ensure server is running and accessible
4. Review API documentation for endpoint details
