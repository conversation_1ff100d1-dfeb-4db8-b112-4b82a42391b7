# Complete API Endpoints List from Backend

## 🔐 Authentication Routes (/auth)
- POST /auth/register
- POST /auth/login  
- POST /auth/logout
- POST /auth/refresh
- POST /auth/forgot-password
- POST /auth/reset-password
- GET /auth/verify-email
- POST /auth/resend-verification

## 🛍️ Public Product Routes (/products)
- GET /products
- GET /products/:id
- GET /products/search
- GET /products/filters
- GET /products/category/:categoryId
- GET /products/featured
- GET /products/trending
- GET /products/:id/reviews (if reviewHandler exists)
- GET /products/:id/rating (if reviewHandler exists)
- GET /products/:id/related
- GET /products/:id/recommendations (if recommendationHandler exists)
- GET /products/:id/frequently-bought-together (if recommendationHandler exists)
- GET /products/suggestions
- GET /products/popular-searches

## 🔒 Authenticated Product Routes (/products)
- GET /products/search-history (requires auth)
- POST /products/filter-sets (requires auth, if productFilterHandler exists)
- GET /products/filter-sets/user (requires auth, if productFilterHandler exists)
- PUT /products/filter-sets/:id (requires auth, if productFilterHandler exists)
- DELETE /products/filter-sets/:id (requires auth, if productFilterHandler exists)

## 📂 Public Category Routes (/categories)
- GET /categories
- GET /categories/:id
- GET /categories/slug/:slug
- GET /categories/tree
- GET /categories/root
- GET /categories/:id/children
- GET /categories/:id/path
- GET /categories/:id/count
- GET /categories/:id/landing
- GET /categories/search
- GET /categories/trending
- GET /categories/popular
- GET /categories/:id/seo

## 🏷️ Public Brand Routes (/brands)
- GET /brands
- GET /brands/active
- GET /brands/popular
- GET /brands/search
- GET /brands/:id
- GET /brands/slug/:slug

## 🛒 Public Cart Routes (/cart)
- GET /cart/guest/:sessionId
- POST /cart/guest/:sessionId/items
- PUT /cart/guest/:sessionId/items/:productId
- DELETE /cart/guest/:sessionId/items/:productId
- DELETE /cart/guest/:sessionId

## 📁 Public File Routes
- POST /public/upload/image (requires auth + rate limit)
- POST /public/upload/document (requires auth + rate limit)
- GET /public/files/:id

## 🚚 Public Shipping Routes (/shipping)
- GET /shipping/methods
- POST /shipping/calculate-distance
- GET /shipping/zones
- POST /shipping/rates
- POST /shipping/validate-address
- GET /shipping/track/:tracking_number

## 🎫 Public Coupon Routes (/coupons)
- POST /coupons/validate

## 🔒 Protected Routes (Requires Authentication)

### 👤 User Routes (/users)
- GET /users/profile
- PUT /users/profile
- POST /users/change-password
- GET /users/preferences
- PUT /users/preferences
- PUT /users/preferences/theme
- PUT /users/preferences/language

### 📍 Address Routes (/addresses)
- GET /addresses
- POST /addresses
- GET /addresses/:id
- PUT /addresses/:id
- DELETE /addresses/:id
- PUT /addresses/:id/default

### 💝 Wishlist Routes (/wishlist)
- GET /wishlist
- POST /wishlist/items
- DELETE /wishlist/items/:productId
- DELETE /wishlist

### ⭐ Review Routes (/reviews)
- POST /reviews
- GET /reviews/user
- PUT /reviews/:id
- DELETE /reviews/:id

### 🛒 Cart Routes (/cart) - Authenticated
- GET /cart
- POST /cart/items
- PUT /cart/items/:productId
- DELETE /cart/items/:productId
- DELETE /cart
- POST /cart/merge
- POST /cart/check-conflict

### 🛍️ Order Routes (/orders)
- POST /orders
- GET /orders
- GET /orders/by-session
- GET /orders/:id
- POST /orders/:id/cancel
- GET /orders/:id/events
- POST /orders/:id/notes
- GET /orders/:id/payments

### 💳 Payment Routes (/payment)
- POST /payment/stripe/create-intent
- POST /payment/stripe/confirm
- POST /payment/webhook

### 🔔 Notification Routes (/notifications)
- GET /notifications
- PUT /notifications/:id/read
- PUT /notifications/read-all
- DELETE /notifications/:id
- GET /notifications/unread-count

### 📁 File Routes (/upload, /files)
- POST /upload/image
- POST /upload/document
- GET /files
- GET /files/:id
- DELETE /files/:id

### 🌐 WebSocket Routes (/ws)
- GET /ws/notifications
- GET /ws/stats (protected)
- GET /ws/users (protected)
- POST /ws/test/:user_id (protected)
- POST /ws/broadcast (protected)

## 🔧 Admin Routes (Requires Admin Authentication)

### 📊 Dashboard Routes (/admin/dashboard)
- GET /admin/dashboard
- GET /admin/dashboard/stats
- GET /admin/dashboard/real-time
- GET /admin/dashboard/activity

### 👥 Admin User Management (/admin/users)
- GET /admin/users
- GET /admin/users/:id
- PUT /admin/users/:id
- DELETE /admin/users/:id
- PUT /admin/users/:id/status
- POST /admin/users/notification
- POST /admin/users/bulk/notification
- POST /admin/users/email
- POST /admin/users/bulk/email
- GET /admin/users/audit-logs
- GET /admin/users/:id/login-history
- GET /admin/users/login-history
- POST /admin/users/announcements

### 🛍️ Admin Product Management (/admin/products)
- POST /admin/products
- PUT /admin/products/:id
- PATCH /admin/products/:id
- DELETE /admin/products/:id
- PUT /admin/products/:id/stock

### 📂 Admin Category Management (/admin/categories)
- POST /admin/categories
- PUT /admin/categories/:id
- DELETE /admin/categories/:id

### 🏷️ Admin Brand Management (/admin/brands)
- POST /admin/brands
- PUT /admin/brands/:id
- DELETE /admin/brands/:id

### 📦 Admin Order Management (/admin/orders)
- GET /admin/orders
- GET /admin/orders/:id
- PUT /admin/orders/:id/status
- PATCH /admin/orders/:id/status
- PUT /admin/orders/:id/shipping
- PUT /admin/orders/:id/delivery
- POST /admin/orders/:id/notes
- GET /admin/orders/:id/events
- POST /admin/orders/:id/refund

### 🎫 Admin Coupon Management (/admin/coupons)
- GET /admin/coupons
- POST /admin/coupons
- GET /admin/coupons/:id
- PUT /admin/coupons/:id
- DELETE /admin/coupons/:id

### 📊 Admin Analytics (/admin/analytics)
- GET /admin/analytics/sales
- GET /admin/analytics/products
- GET /admin/analytics/users
- GET /admin/analytics/traffic
- POST /admin/analytics/events
- GET /admin/analytics/top-products
- GET /admin/analytics/top-categories
- GET /admin/analytics/filters (if productFilterHandler exists)
- GET /admin/analytics/filters/popular (if productFilterHandler exists)

### 📋 Admin Reports (/admin/reports)
- POST /admin/reports/generate
- GET /admin/reports
- GET /admin/reports/:id/download

### ⚙️ Admin System Management (/admin/system)
- GET /admin/system/logs
- GET /admin/system/audit
- POST /admin/system/backup
- GET /admin/system/cleanup/stats
- POST /admin/system/cleanup/trigger

### 🔒 Admin Security (/admin/security)
- GET /admin/security/suspicious-activity
- GET /admin/security/report

## 🛡️ Moderator Routes (Requires Moderator Authentication)

### 🛍️ Moderator Product Management (/moderator/products)
- POST /moderator/products
- PUT /moderator/products/:id
- PATCH /moderator/products/:id
- PUT /moderator/products/:id/stock

### 📁 Moderator File Management (/moderator/upload)
- POST /moderator/upload/image
- POST /moderator/upload/document

## 📊 Total Count
- **Authentication**: 8 endpoints
- **Public Products**: 12 endpoints
- **Authenticated Products**: 4 endpoints  
- **Public Categories**: 13 endpoints
- **Public Brands**: 6 endpoints
- **Public Cart**: 5 endpoints
- **Public Files**: 3 endpoints
- **Public Shipping**: 6 endpoints
- **Public Coupons**: 1 endpoint
- **Protected User**: 7 endpoints
- **Protected Address**: 6 endpoints
- **Protected Wishlist**: 4 endpoints
- **Protected Reviews**: 4 endpoints
- **Protected Cart**: 6 endpoints
- **Protected Orders**: 8 endpoints
- **Protected Payment**: 3 endpoints
- **Protected Notifications**: 5 endpoints
- **Protected Files**: 5 endpoints
- **Protected WebSocket**: 5 endpoints
- **Admin Dashboard**: 4 endpoints
- **Admin Users**: 13 endpoints
- **Admin Products**: 5 endpoints
- **Admin Categories**: 3 endpoints
- **Admin Brands**: 3 endpoints
- **Admin Orders**: 9 endpoints
- **Admin Coupons**: 5 endpoints
- **Admin Analytics**: 9 endpoints
- **Admin Reports**: 3 endpoints
- **Admin System**: 5 endpoints
- **Admin Security**: 2 endpoints
- **Moderator Products**: 4 endpoints
- **Moderator Files**: 2 endpoints

**TOTAL: ~180+ API endpoints**
