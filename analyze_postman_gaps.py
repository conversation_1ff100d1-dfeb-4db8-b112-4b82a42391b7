#!/usr/bin/env python3
"""
Script to analyze gaps between backend API endpoints and Postman collection
"""

import json
import re
from typing import Dict, List, Set, Tuple

def extract_postman_endpoints(postman_file: str) -> Set[Tuple[str, str]]:
    """Extract all endpoints from Postman collection"""
    endpoints = set()
    
    with open(postman_file, 'r') as f:
        data = json.load(f)
    
    def extract_from_item(item):
        if 'request' in item:
            method = item['request'].get('method', '')
            if 'url' in item['request']:
                if isinstance(item['request']['url'], dict):
                    path_parts = item['request']['url'].get('path', [])
                    if path_parts:
                        # Convert path parts to URL path
                        path = '/' + '/'.join(path_parts)
                        # Replace variables like {{base_url}} and {{variable}}
                        path = re.sub(r'/api/v1', '', path)  # Remove base path
                        path = re.sub(r'\{\{[^}]+\}\}', ':param', path)  # Replace variables
                        endpoints.add((method, path))
                elif isinstance(item['request']['url'], str):
                    # Handle string URLs
                    url = item['request']['url']
                    # Extract path from URL
                    if '/api/v1' in url:
                        path = url.split('/api/v1')[1].split('?')[0]
                        path = re.sub(r'\{\{[^}]+\}\}', ':param', path)
                        endpoints.add((method, path))
        
        # Recursively process nested items
        if 'item' in item:
            for subitem in item['item']:
                extract_from_item(subitem)
    
    # Process all top-level items
    for item in data.get('item', []):
        extract_from_item(item)
    
    return endpoints

def get_backend_endpoints() -> Set[Tuple[str, str]]:
    """Define all backend endpoints from routes.go analysis"""
    endpoints = set()
    
    # Authentication Routes
    auth_routes = [
        ('POST', '/auth/register'),
        ('POST', '/auth/login'),
        ('POST', '/auth/logout'),
        ('POST', '/auth/refresh'),
        ('POST', '/auth/forgot-password'),
        ('POST', '/auth/reset-password'),
        ('GET', '/auth/verify-email'),
        ('POST', '/auth/resend-verification'),
    ]
    endpoints.update(auth_routes)
    
    # Public Product Routes
    product_routes = [
        ('GET', '/products'),
        ('GET', '/products/:param'),
        ('GET', '/products/search'),
        ('GET', '/products/filters'),
        ('GET', '/products/category/:param'),
        ('GET', '/products/featured'),
        ('GET', '/products/trending'),
        ('GET', '/products/:param/reviews'),
        ('GET', '/products/:param/rating'),
        ('GET', '/products/:param/related'),
        ('GET', '/products/:param/recommendations'),
        ('GET', '/products/:param/frequently-bought-together'),
        ('GET', '/products/suggestions'),
        ('GET', '/products/popular-searches'),
        ('GET', '/products/search-history'),  # Auth required
        ('POST', '/products/filter-sets'),    # Auth required
        ('GET', '/products/filter-sets/user'), # Auth required
        ('PUT', '/products/filter-sets/:param'), # Auth required
        ('DELETE', '/products/filter-sets/:param'), # Auth required
    ]
    endpoints.update(product_routes)
    
    # Public Category Routes
    category_routes = [
        ('GET', '/categories'),
        ('GET', '/categories/:param'),
        ('GET', '/categories/slug/:param'),
        ('GET', '/categories/tree'),
        ('GET', '/categories/root'),
        ('GET', '/categories/:param/children'),
        ('GET', '/categories/:param/path'),
        ('GET', '/categories/:param/count'),
        ('GET', '/categories/:param/landing'),
        ('GET', '/categories/search'),
        ('GET', '/categories/trending'),
        ('GET', '/categories/popular'),
        ('GET', '/categories/:param/seo'),
    ]
    endpoints.update(category_routes)
    
    # Public Brand Routes
    brand_routes = [
        ('GET', '/brands'),
        ('GET', '/brands/active'),
        ('GET', '/brands/popular'),
        ('GET', '/brands/search'),
        ('GET', '/brands/:param'),
        ('GET', '/brands/slug/:param'),
    ]
    endpoints.update(brand_routes)
    
    # Cart Routes (Public)
    cart_public_routes = [
        ('GET', '/cart/guest/:param'),
        ('POST', '/cart/guest/:param/items'),
        ('PUT', '/cart/guest/:param/items/:param'),
        ('DELETE', '/cart/guest/:param/items/:param'),
        ('DELETE', '/cart/guest/:param'),
    ]
    endpoints.update(cart_public_routes)
    
    # Cart Routes (Protected)
    cart_protected_routes = [
        ('GET', '/cart'),
        ('POST', '/cart/items'),
        ('PUT', '/cart/items/:param'),
        ('DELETE', '/cart/items/:param'),
        ('DELETE', '/cart'),
        ('POST', '/cart/merge'),
        ('POST', '/cart/check-conflict'),
    ]
    endpoints.update(cart_protected_routes)
    
    # User Routes (Protected)
    user_routes = [
        ('GET', '/users/profile'),
        ('PUT', '/users/profile'),
        ('POST', '/users/change-password'),
        ('GET', '/users/preferences'),
        ('PUT', '/users/preferences'),
        ('PUT', '/users/preferences/theme'),
        ('PUT', '/users/preferences/language'),
    ]
    endpoints.update(user_routes)
    
    # Address Routes (Protected)
    address_routes = [
        ('GET', '/addresses'),
        ('POST', '/addresses'),
        ('GET', '/addresses/:param'),
        ('PUT', '/addresses/:param'),
        ('DELETE', '/addresses/:param'),
        ('PUT', '/addresses/:param/default'),
    ]
    endpoints.update(address_routes)
    
    # Wishlist Routes (Protected)
    wishlist_routes = [
        ('GET', '/wishlist'),
        ('POST', '/wishlist/items'),
        ('DELETE', '/wishlist/items/:param'),
        ('DELETE', '/wishlist'),
    ]
    endpoints.update(wishlist_routes)
    
    # Review Routes (Protected)
    review_routes = [
        ('POST', '/reviews'),
        ('GET', '/reviews/user'),
        ('PUT', '/reviews/:param'),
        ('DELETE', '/reviews/:param'),
    ]
    endpoints.update(review_routes)
    
    # Order Routes (Protected)
    order_routes = [
        ('POST', '/orders'),
        ('GET', '/orders'),
        ('GET', '/orders/by-session'),
        ('GET', '/orders/:param'),
        ('POST', '/orders/:param/cancel'),
        ('GET', '/orders/:param/events'),
        ('POST', '/orders/:param/notes'),
        ('GET', '/orders/:param/payments'),
    ]
    endpoints.update(order_routes)
    
    # Payment Routes
    payment_routes = [
        ('POST', '/payment/stripe/create-intent'),
        ('POST', '/payment/stripe/confirm'),
        ('POST', '/payment/webhook'),
    ]
    endpoints.update(payment_routes)
    
    # Notification Routes (Protected)
    notification_routes = [
        ('GET', '/notifications'),
        ('PUT', '/notifications/:param/read'),
        ('PUT', '/notifications/read-all'),
        ('DELETE', '/notifications/:param'),
        ('GET', '/notifications/unread-count'),
    ]
    endpoints.update(notification_routes)
    
    # File Routes
    file_routes = [
        ('POST', '/public/upload/image'),
        ('POST', '/public/upload/document'),
        ('GET', '/public/files/:param'),
        ('POST', '/upload/image'),
        ('POST', '/upload/document'),
        ('GET', '/files'),
        ('GET', '/files/:param'),
        ('DELETE', '/files/:param'),
    ]
    endpoints.update(file_routes)
    
    # Shipping Routes
    shipping_routes = [
        ('GET', '/shipping/methods'),
        ('POST', '/shipping/calculate-distance'),
        ('GET', '/shipping/zones'),
        ('POST', '/shipping/rates'),
        ('POST', '/shipping/validate-address'),
        ('GET', '/shipping/track/:param'),
    ]
    endpoints.update(shipping_routes)
    
    # Coupon Routes
    coupon_routes = [
        ('POST', '/coupons/validate'),
    ]
    endpoints.update(coupon_routes)
    
    # WebSocket Routes
    websocket_routes = [
        ('GET', '/ws/notifications'),
        ('GET', '/ws/stats'),
        ('GET', '/ws/users'),
        ('POST', '/ws/test/:param'),
        ('POST', '/ws/broadcast'),
    ]
    endpoints.update(websocket_routes)
    
    # Admin Routes
    admin_routes = [
        # Dashboard
        ('GET', '/admin/dashboard'),
        ('GET', '/admin/dashboard/stats'),
        ('GET', '/admin/dashboard/real-time'),
        ('GET', '/admin/dashboard/activity'),
        
        # Users
        ('GET', '/admin/users'),
        ('GET', '/admin/users/:param'),
        ('PUT', '/admin/users/:param'),
        ('DELETE', '/admin/users/:param'),
        ('PUT', '/admin/users/:param/status'),
        ('POST', '/admin/users/notification'),
        ('POST', '/admin/users/bulk/notification'),
        ('POST', '/admin/users/email'),
        ('POST', '/admin/users/bulk/email'),
        ('GET', '/admin/users/audit-logs'),
        ('GET', '/admin/users/:param/login-history'),
        ('GET', '/admin/users/login-history'),
        ('POST', '/admin/users/announcements'),
        
        # Products
        ('POST', '/admin/products'),
        ('PUT', '/admin/products/:param'),
        ('PATCH', '/admin/products/:param'),
        ('DELETE', '/admin/products/:param'),
        ('PUT', '/admin/products/:param/stock'),
        
        # Categories
        ('POST', '/admin/categories'),
        ('PUT', '/admin/categories/:param'),
        ('DELETE', '/admin/categories/:param'),
        
        # Brands
        ('POST', '/admin/brands'),
        ('PUT', '/admin/brands/:param'),
        ('DELETE', '/admin/brands/:param'),
        
        # Orders
        ('GET', '/admin/orders'),
        ('GET', '/admin/orders/:param'),
        ('PUT', '/admin/orders/:param/status'),
        ('PATCH', '/admin/orders/:param/status'),
        ('PUT', '/admin/orders/:param/shipping'),
        ('PUT', '/admin/orders/:param/delivery'),
        ('POST', '/admin/orders/:param/notes'),
        ('GET', '/admin/orders/:param/events'),
        ('POST', '/admin/orders/:param/refund'),
        
        # Coupons
        ('GET', '/admin/coupons'),
        ('POST', '/admin/coupons'),
        ('GET', '/admin/coupons/:param'),
        ('PUT', '/admin/coupons/:param'),
        ('DELETE', '/admin/coupons/:param'),
        
        # Analytics
        ('GET', '/admin/analytics/sales'),
        ('GET', '/admin/analytics/products'),
        ('GET', '/admin/analytics/users'),
        ('GET', '/admin/analytics/traffic'),
        ('POST', '/admin/analytics/events'),
        ('GET', '/admin/analytics/top-products'),
        ('GET', '/admin/analytics/top-categories'),
        ('GET', '/admin/analytics/filters'),
        ('GET', '/admin/analytics/filters/popular'),
        
        # Reports
        ('POST', '/admin/reports/generate'),
        ('GET', '/admin/reports'),
        ('GET', '/admin/reports/:param/download'),
        
        # System
        ('GET', '/admin/system/logs'),
        ('GET', '/admin/system/audit'),
        ('POST', '/admin/system/backup'),
        ('GET', '/admin/system/cleanup/stats'),
        ('POST', '/admin/system/cleanup/trigger'),
        
        # Security
        ('GET', '/admin/security/suspicious-activity'),
        ('GET', '/admin/security/report'),
    ]
    endpoints.update(admin_routes)
    
    # Moderator Routes
    moderator_routes = [
        ('POST', '/moderator/products'),
        ('PUT', '/moderator/products/:param'),
        ('PATCH', '/moderator/products/:param'),
        ('PUT', '/moderator/products/:param/stock'),
        ('POST', '/moderator/upload/image'),
        ('POST', '/moderator/upload/document'),
    ]
    endpoints.update(moderator_routes)
    
    return endpoints

def main():
    print("🔍 Analyzing Postman Collection Gaps...")
    
    # Extract endpoints from Postman collection
    postman_endpoints = extract_postman_endpoints('postman-collection/Ecommerce-API-Collection.postman_collection.json')
    
    # Get backend endpoints
    backend_endpoints = get_backend_endpoints()
    
    print(f"\n📊 Statistics:")
    print(f"Backend endpoints: {len(backend_endpoints)}")
    print(f"Postman endpoints: {len(postman_endpoints)}")
    
    # Find missing endpoints in Postman
    missing_in_postman = backend_endpoints - postman_endpoints
    
    # Find extra endpoints in Postman (not in backend)
    extra_in_postman = postman_endpoints - backend_endpoints
    
    print(f"\n❌ Missing in Postman: {len(missing_in_postman)}")
    if missing_in_postman:
        for method, path in sorted(missing_in_postman):
            print(f"  {method} {path}")
    
    print(f"\n➕ Extra in Postman: {len(extra_in_postman)}")
    if extra_in_postman:
        for method, path in sorted(extra_in_postman):
            print(f"  {method} {path}")
    
    print(f"\n✅ Coverage: {((len(postman_endpoints) - len(extra_in_postman)) / len(backend_endpoints) * 100):.1f}%")

if __name__ == "__main__":
    main()
